{"ast": null, "code": "/**\n * @returns {string}\n */\nfunction getCurrentScriptSource() {\n  // `document.currentScript` is the most accurate way to find the current script,\n  // but is not supported in all browsers.\n  if (document.currentScript) {\n    return document.currentScript.getAttribute(\"src\");\n  }\n\n  // Fallback to getting all scripts running in the document.\n  var scriptElements = document.scripts || [];\n  var scriptElementsWithSrc = Array.prototype.filter.call(scriptElements, function (element) {\n    return element.getAttribute(\"src\");\n  });\n  if (scriptElementsWithSrc.length > 0) {\n    var currentScript = scriptElementsWithSrc[scriptElementsWithSrc.length - 1];\n    return currentScript.getAttribute(\"src\");\n  }\n\n  // Fail as there was no script to use.\n  throw new Error(\"[webpack-dev-server] Failed to get current script source.\");\n}\nexport default getCurrentScriptSource;", "map": {"version": 3, "names": ["getCurrentScriptSource", "document", "currentScript", "getAttribute", "scriptElements", "scripts", "scriptElementsWithSrc", "Array", "prototype", "filter", "call", "element", "length", "Error"], "sources": ["D:/Sanket/Outschool Demo/outschool-clone/node_modules/webpack-dev-server/client/utils/getCurrentScriptSource.js"], "sourcesContent": ["/**\n * @returns {string}\n */\nfunction getCurrentScriptSource() {\n  // `document.currentScript` is the most accurate way to find the current script,\n  // but is not supported in all browsers.\n  if (document.currentScript) {\n    return document.currentScript.getAttribute(\"src\");\n  }\n\n  // Fallback to getting all scripts running in the document.\n  var scriptElements = document.scripts || [];\n  var scriptElementsWithSrc = Array.prototype.filter.call(scriptElements, function (element) {\n    return element.getAttribute(\"src\");\n  });\n  if (scriptElementsWithSrc.length > 0) {\n    var currentScript = scriptElementsWithSrc[scriptElementsWithSrc.length - 1];\n    return currentScript.getAttribute(\"src\");\n  }\n\n  // Fail as there was no script to use.\n  throw new Error(\"[webpack-dev-server] Failed to get current script source.\");\n}\nexport default getCurrentScriptSource;"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,sBAAsBA,CAAA,EAAG;EAChC;EACA;EACA,IAAIC,QAAQ,CAACC,aAAa,EAAE;IAC1B,OAAOD,QAAQ,CAACC,aAAa,CAACC,YAAY,CAAC,KAAK,CAAC;EACnD;;EAEA;EACA,IAAIC,cAAc,GAAGH,QAAQ,CAACI,OAAO,IAAI,EAAE;EAC3C,IAAIC,qBAAqB,GAAGC,KAAK,CAACC,SAAS,CAACC,MAAM,CAACC,IAAI,CAACN,cAAc,EAAE,UAAUO,OAAO,EAAE;IACzF,OAAOA,OAAO,CAACR,YAAY,CAAC,KAAK,CAAC;EACpC,CAAC,CAAC;EACF,IAAIG,qBAAqB,CAACM,MAAM,GAAG,CAAC,EAAE;IACpC,IAAIV,aAAa,GAAGI,qBAAqB,CAACA,qBAAqB,CAACM,MAAM,GAAG,CAAC,CAAC;IAC3E,OAAOV,aAAa,CAACC,YAAY,CAAC,KAAK,CAAC;EAC1C;;EAEA;EACA,MAAM,IAAIU,KAAK,CAAC,2DAA2D,CAAC;AAC9E;AACA,eAAeb,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}